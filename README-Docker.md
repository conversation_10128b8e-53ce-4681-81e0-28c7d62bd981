# AI写作平台后端 - Docker部署指南

本文档提供了使用Docker部署AI写作平台后端API的详细说明。

## 项目结构

Docker相关文件结构：

```
story-api/
├── Dockerfile              # 定义应用程序容器
├── docker-compose.yml      # 定义服务组合
├── .dockerignore           # 指定不包含在Docker镜像中的文件
└── docker/
    └── README.md           # Docker部署详细说明
```

## 快速开始

### 1. 证书配置

MongoDB的TLS证书已经放置在`app/core/certs`文件夹中，Docker配置已经设置为使用这些证书。

### 2. 配置环境变量

确保项目根目录下的`.env`文件包含正确的MongoDB连接信息。

### 3. 启动服务

使用Docker Compose启动服务：

```bash
# 构建并启动容器
docker-compose up -d

# 查看日志
docker-compose logs -f
```

### 4. 访问API

服务启动后，可以通过以下URL访问API：

- API文档：http://your-server-ip:8000/docs
- API端点：http://your-server-ip:8000/api/v1

## 配置说明

### 环境变量

`.env` 文件包含了应用程序的配置，主要包括：

- MongoDB连接设置
- API配置
- 安全设置

### 应用服务

应用服务使用Python 3.10和FastAPI框架，通过uvicorn服务器运行。

## 生产环境部署注意事项

1. **安全性**：
   - 确保MongoDB连接使用TLS/SSL加密
   - 使用强密码和密钥
   - 考虑使用环境变量而不是.env文件

2. **性能优化**：
   - 根据服务器资源调整uvicorn工作进程数
   - 考虑使用Nginx作为反向代理

3. **监控**：
   - 设置日志收集和监控系统
   - 配置健康检查和自动重启

## 故障排除

如果遇到问题，请检查：

1. MongoDB连接字符串是否正确
2. 证书是否正确配置（如果需要）
3. 容器日志中是否有错误信息

更多详细信息，请参考 `docker/README.md` 文件。
