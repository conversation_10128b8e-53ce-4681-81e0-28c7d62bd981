from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field
from bson import ObjectId
from .user import PyObjectId

class MessageBase(BaseModel):
    conversation_id: str
    sequence: int
    agent_type: Optional[str] = None  # 架构师/记者/执笔人
    role: str  # user/agent
    content: str

class MessageCreate(BaseModel):
    conversation_id: str  # 会话ID，现在在请求体中
    agent_type: Optional[str] = None
    role: str = "user"  # 默认为用户消息
    content: str

class MessageInDB(MessageBase):
    id: PyObjectId = Field(default_factory=lambda: str(ObjectId()), alias="_id")
    user_id: str  # 消息所属用户
    timestamp: datetime = Field(default_factory=lambda: datetime.now(datetime.timezone.utc))

    model_config = {
        "populate_by_name": True,
        "json_schema_extra": {
            "example": {
                "conversation_id": "conv123",
                "sequence": 1,
                "agent_type": "架构师",
                "role": "user",
                "content": "请帮我构思一个科幻故事",
                "user_id": "user123",
                "timestamp": "2023-01-01T00:00:00Z"
            }
        }
    }

class Message(MessageBase):
    id: PyObjectId = Field(alias="_id")
    user_id: str
    timestamp: datetime

    model_config = {
        "populate_by_name": True,
        "json_schema_extra": {
            "example": {
                "id": "507f1f77bcf86cd799439011",
                "conversation_id": "conv123",
                "sequence": 1,
                "agent_type": "架构师",
                "role": "user",
                "content": "请帮我构思一个科幻故事",
                "user_id": "user123",
                "timestamp": "2023-01-01T00:00:00Z"
            }
        }
    }

class MessageResponse(BaseModel):
    """用于API响应的消息模型"""
    id: str
    conversation_id: str
    sequence: int
    agent_type: Optional[str] = None
    role: str
    content: str
    user_id: str
    timestamp: datetime

    model_config = {
        "json_schema_extra": {
            "example": {
                "id": "507f1f77bcf86cd799439011",
                "conversation_id": "conv123",
                "sequence": 1,
                "agent_type": "架构师",
                "role": "user",
                "content": "请帮我构思一个科幻故事",
                "user_id": "user123",
                "timestamp": "2023-01-01T00:00:00Z"
            }
        }
    }

class MessageWithAIResponse(BaseModel):
    """发送消息后的响应，包含用户消息和AI回复"""
    user_message: MessageResponse
    ai_message: MessageResponse

    model_config = {
        "json_schema_extra": {
            "example": {
                "user_message": {
                    "id": "507f1f77bcf86cd799439011",
                    "conversation_id": "conv123",
                    "sequence": 1,
                    "agent_type": None,
                    "role": "user",
                    "content": "请帮我构思一个科幻故事",
                    "user_id": "user123",
                    "timestamp": "2023-01-01T00:00:00Z"
                },
                "ai_message": {
                    "id": "507f1f77bcf86cd799439012",
                    "conversation_id": "conv123",
                    "sequence": 2,
                    "agent_type": "架构师",
                    "role": "agent",
                    "content": "我来帮您构思一个科幻故事...",
                    "user_id": "user123",
                    "timestamp": "2023-01-01T00:00:01Z"
                }
            }
        }
    }
