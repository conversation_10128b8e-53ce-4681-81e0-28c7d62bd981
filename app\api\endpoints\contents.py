from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException, status, Query

from ...models.content import Content, ContentCreate, ContentUpdate
from ...models.user import User
from ...services.content_service import (
    create_content,
    get_content_by_id,
    get_contents_by_user,
    update_content,
    delete_content
)
from .auth import get_current_user

router = APIRouter()

@router.post("/", response_model=Content)
async def create_new_content(
    content_data: ContentCreate,
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    Create new content.
    """
    # Set the user_id to the current user's ID
    content_data.user_id = str(current_user.id)

    content = await create_content(content_data)
    return content

@router.get("/", response_model=List[Content])
async def read_contents(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    Retrieve contents for current user.
    """
    contents = await get_contents_by_user(str(current_user.id), skip=skip, limit=limit)
    return contents

@router.get("/{content_id}", response_model=Content)
async def read_content(
    content_id: str,
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    Get a specific content by ID.
    """
    content = await get_content_by_id(content_id)
    if not content:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Content not found",
        )

    # Check if the content belongs to the current user
    if content.user_id != str(current_user.id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions",
        )

    return content

@router.put("/{content_id}", response_model=Content)
async def update_content_by_id(
    content_id: str,
    content_data: ContentUpdate,
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    Update a content.
    """
    content = await get_content_by_id(content_id)
    if not content:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Content not found",
        )

    # Check if the content belongs to the current user
    if content.user_id != str(current_user.id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions",
        )

    updated_content = await update_content(content_id, content_data)
    return updated_content

@router.delete("/{content_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_content_by_id(
    content_id: str,
    current_user: User = Depends(get_current_user)
) -> None:
    """
    Delete a content.
    """
    content = await get_content_by_id(content_id)
    if not content:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Content not found",
        )

    # Check if the content belongs to the current user
    if content.user_id != str(current_user.id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions",
        )

    success = await delete_content(content_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete content",
        )
