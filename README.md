# AI写作平台后端

基于FastAPI和MongoDB构建的AI写作平台后端API。

## 功能特点

- 用户认证与管理
- 内容创建与管理
- RESTful API设计
- MongoDB数据库集成

## 系统要求

- Python 3.10+
- 云端MongoDB数据库连接

## 本地开发环境搭建

1. 克隆仓库
2. 创建并激活虚拟环境：
   ```
   python -m venv venv
   .\venv\Scripts\activate  # Windows
   source venv/bin/activate  # Linux/Mac
   ```
3. 安装依赖：
   ```
   pip install -r requirements.txt
   ```
4. 在`.env`文件中配置环境变量，确保MongoDB连接字符串指向云端数据库
5. 运行应用：
   ```
   python run.py
   ```
   或者
   ```
   uvicorn app.main:app --reload
   ```

## Docker部署

### 前提条件

- 安装Docker和Docker Compose
- 已有可用的云端MongoDB服务器

### 部署步骤

1. **证书配置**：MongoDB的TLS证书已放置在`app/core/certs`文件夹中，用于安全连接云端数据库

2. **配置环境变量**：确保项目根目录下的`.env`文件包含正确的云端MongoDB连接信息

3. **启动服务**：
   ```bash
   # 构建并启动容器
   docker-compose up -d

   # 查看日志
   docker-compose logs -f
   ```

4. **访问API**：
   - API文档：http://your-server-ip:8000/docs
   - API端点：http://your-server-ip:8000/api/v1

更多Docker部署详情，请参考`README-Docker.md`文件。

## API文档

应用程序运行后，您可以在以下位置访问API文档：
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## API端点

- **认证**：
  - POST `/api/v1/auth/register` - 注册新用户
  - POST `/api/v1/auth/token` - 获取访问令牌

- **用户**：
  - GET `/api/v1/users/me` - 获取当前用户信息
  - PUT `/api/v1/users/me` - 更新当前用户信息

- **内容**：
  - GET `/api/v1/contents` - 列出用户内容
  - POST `/api/v1/contents` - 创建新内容
  - GET `/api/v1/contents/{content_id}` - 获取特定内容
  - PUT `/api/v1/contents/{content_id}` - 更新内容
  - DELETE `/api/v1/contents/{content_id}` - 删除内容
