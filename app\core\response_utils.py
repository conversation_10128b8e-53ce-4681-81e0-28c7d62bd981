from typing import Any, Dict, Optional, TypeVar
from fastapi.responses import JSONResponse
from fastapi.encoders import jsonable_encoder
import json
from datetime import datetime, date
from ..models.response import ErrorCodes

T = TypeVar('T')

# 自定义 JSON 编码器，处理 datetime 和其他特殊类型
class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, (datetime, date)):
            return obj.isoformat()
        return jsonable_encoder(obj)

def create_response(
    data: Any = None,
    status_code: int = 0,
    message: Optional[str] = None
) -> Dict[str, Any]:
    """
    Create a standardized response dictionary.

    Args:
        data: The response data
        status_code: Status code (0 for success, other values for errors)
        message: Optional message

    Returns:
        A dictionary with the standardized response format
    """
    return {
        "status": status_code,
        "data": data,
        "message": message
    }

def success_response(data: Any = None, message: str = "Operation successful") -> Dict[str, Any]:
    """
    Create a success response.

    Args:
        data: The response data
        message: Success message

    Returns:
        A dictionary with the standardized success response
    """
    return create_response(data=data, status_code=0, message=message)

def error_response(
    error_code: int = ErrorCodes.GENERAL_ERROR,
    message: str = "An error occurred",
    data: Any = None
) -> Dict[str, Any]:
    """
    Create an error response.

    Args:
        error_code: Error code
        message: Error message
        data: Additional error data

    Returns:
        A dictionary with the standardized error response
    """
    return create_response(data=data, status_code=error_code, message=message)

def not_found_response(message: str = "Resource not found") -> Dict[str, Any]:
    """Create a not found error response."""
    return error_response(error_code=ErrorCodes.NOT_FOUND_ERROR, message=message)

def validation_error_response(message: str = "Validation error", errors: Any = None) -> Dict[str, Any]:
    """Create a validation error response with the validation errors."""
    return error_response(error_code=ErrorCodes.VALIDATION_ERROR, message=message, data=errors)

def auth_error_response(message: str = "Authentication error") -> Dict[str, Any]:
    """Create an authentication error response."""
    return error_response(error_code=ErrorCodes.AUTHENTICATION_ERROR, message=message)

class StandardJSONResponse(JSONResponse):
    """
    Custom JSON response class that automatically wraps the response content
    in the standardized format.
    """
    def __init__(
        self,
        content: Any = None,
        status_code: int = 200,
        headers: Dict[str, str] = None,
        media_type: str = None,
        background=None,
    ):
        # Check if the content is already in the standard format
        if isinstance(content, dict) and all(k in content for k in ["status", "data", "message"]):
            standardized_content = content
        else:
            # Wrap the content in the standard format
            standardized_content = {
                "status": 0,  # Success
                "data": content,
                "message": None
            }

        # Call the parent constructor with the standardized content
        super().__init__(
            content=standardized_content,
            status_code=status_code,
            headers=headers,
            media_type=media_type,
            background=background,
        )

    def render(self, content: Any) -> bytes:
        """
        Override the render method to use our custom JSON encoder.
        This handles datetime objects and other special types.
        """
        return json.dumps(
            content,
            ensure_ascii=False,
            allow_nan=False,
            indent=None,
            separators=(",", ":"),
            cls=CustomJSONEncoder,
        ).encode("utf-8")
