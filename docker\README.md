# Docker部署指南

本文档提供了使用Docker部署AI写作平台后端API的详细说明。

## 前提条件

- 安装Docker和Docker Compose
  - [Docker安装指南](https://docs.docker.com/get-docker/)
  - [Docker Compose安装指南](https://docs.docker.com/compose/install/)
- 已有可用的MongoDB服务器

## 部署步骤

### 1. 证书配置

MongoDB的TLS证书已经放置在`app/core/certs`文件夹中，Docker配置已经设置为使用这些证书。

### 2. 配置环境变量

确保项目根目录下的`.env`文件包含正确的MongoDB连接信息：

```
MONGODB_URL=*********************************************************************************************
MONGODB_DB=database-name
```

### 3. 启动服务

使用Docker Compose启动服务：

```bash
# 构建并启动容器
docker-compose up -d

# 查看日志
docker-compose logs -f
```

### 4. 访问API

服务启动后，可以通过以下URL访问API：

- API文档：http://your-server-ip:8000/docs
- API端点：http://your-server-ip:8000/api/v1

## 常用命令

```bash
# 启动服务
docker-compose up -d

# 停止服务
docker-compose down

# 重启服务
docker-compose restart

# 查看日志
docker-compose logs -f

# 查看容器状态
docker-compose ps

# 重新构建镜像
docker-compose build --no-cache
```

## 故障排除

如果遇到连接问题，请检查：

1. MongoDB连接字符串是否正确
2. TLS/SSL证书是否正确配置
3. 网络设置是否允许所需端口的流量
