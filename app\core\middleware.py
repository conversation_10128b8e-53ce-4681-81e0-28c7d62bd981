from typing import Callable
from fastapi import Request, Response
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGI<PERSON>pp
from .response_utils import CustomJ<PERSON><PERSON>ncoder, StandardJSONResponse
import json

class StandardResponseMiddleware(BaseHTTPMiddleware):
    """
    Middleware to standardize all API responses to the format:
    {
        "status": 0,  # 0 for success, other values for errors
        "data": {...},  # The actual response data
        "message": "..."  # Optional message
    }
    """

    def __init__(self, app: ASGIApp):
        super().__init__(app)

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Process the request and get the response
        response = await call_next(request)

        # Skip non-JSON responses (like HTML, static files, etc.)
        if not isinstance(response, JSONResponse):
            return response

        # Skip responses that are already in our standard format
        if self._is_standard_format(response):
            return response

        # Get the original response content
        response_body = response.body

        try:
            # Try to parse the response body as JSON
            original_data = json.loads(response_body)

            # Wrap the response in our standard format
            new_content = {
                "status": 0,  # Success
                "data": original_data,
                "message": None
            }

            # Create a new response with the standardized format using our custom response class
            return JSONResponse(
                content=new_content,
                status_code=response.status_code,
                headers=dict(response.headers),
                media_type=response.media_type
            )
        except Exception:
            # If we can't parse the response as JSON, return it as is
            return response

    def _is_standard_format(self, response: JSONResponse) -> bool:
        """Check if the response is already in our standard format."""
        try:
            content = json.loads(response.body)
            return (
                isinstance(content, dict) and
                "status" in content and
                "data" in content and
                "message" in content
            )
        except Exception:
            return False
