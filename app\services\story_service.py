from datetime import datetime, timezone
from typing import Optional, List
import logging
from bson import ObjectId
from ..db.mongodb import mongodb
from ..models.story import StoryCreate, StoryInDB, StoryUpdate

async def get_story_by_id(story_id: str, user_id: str = None) -> Optional[StoryInDB]:
    """根据ID获取故事，可选择验证用户权限"""
    logging.info(f"查询故事: story_id={story_id}, user_id={user_id}")

    if not mongodb.connected or mongodb.db is None:
        logging.error("数据库未连接")
        return None

    if not ObjectId.is_valid(story_id):
        logging.error(f"无效的ObjectId格式: {story_id}")
        return None

    # 构建查询条件 - 尝试两种ID格式
    queries_to_try = [
        {"_id": ObjectId(story_id)},  # ObjectId 格式
        {"_id": story_id}             # 字符串格式
    ]

    if user_id:
        queries_to_try = [
            {"_id": ObjectId(story_id), "user_id": user_id},
            {"_id": story_id, "user_id": user_id}
        ]

    story = None
    for query in queries_to_try:
        logging.info(f"尝试查询条件: {query}")
        story = await mongodb.db.stories.find_one(query)
        logging.info(f"查询结果: {story}")
        if story:
            break

    if story:
        return StoryInDB(**story)
    return None

async def get_stories_by_user(user_id: str, skip: int = 0, limit: int = 100) -> List[StoryInDB]:
    """获取用户的所有故事"""
    if not mongodb.connected or mongodb.db is None:
        return []
    
    stories = []
    cursor = mongodb.db.stories.find({"user_id": user_id}).skip(skip).limit(limit).sort("updated_at", -1)
    async for story in cursor:
        stories.append(StoryInDB(**story))
    return stories

async def create_story(story_data: StoryCreate) -> StoryInDB:
    """创建新故事"""
    if not mongodb.connected or mongodb.db is None:
        raise ValueError("数据库连接不可用")
    
    current_time = datetime.now(timezone.utc)
    story_in_db = StoryInDB(
        **story_data.model_dump(),
        created_at=current_time,
        updated_at=current_time
    )
    
    # 插入数据库
    result = await mongodb.db.stories.insert_one(story_in_db.model_dump(by_alias=True))
    
    # 更新ID字段
    story_in_db.id = result.inserted_id
    
    return story_in_db

async def update_story(story_id: str, story_data: StoryUpdate, user_id: str) -> Optional[StoryInDB]:
    """更新故事信息"""
    if not mongodb.connected or mongodb.db is None:
        return None
    
    # 首先验证故事存在且属于当前用户
    story = await get_story_by_id(story_id, user_id)
    if not story:
        return None
    
    # 准备更新数据
    update_data = story_data.model_dump(exclude_unset=True)
    if not update_data:  # 如果没有要更新的数据
        return story
    
    update_data["updated_at"] = datetime.now(timezone.utc)
    
    # 更新数据库
    try:
        result = await mongodb.db.stories.update_one(
            {"_id": ObjectId(story_id), "user_id": user_id},
            {"$set": update_data}
        )
        
        if result.matched_count == 0:
            return None
            
    except Exception as e:
        logging.error(f"更新故事时出错: {e}")
        return None
    
    return await get_story_by_id(story_id, user_id)

async def delete_story(story_id: str, user_id: str) -> bool:
    """删除故事"""
    if not mongodb.connected or mongodb.db is None:
        return False
    
    if not ObjectId.is_valid(story_id):
        return False
    
    try:
        result = await mongodb.db.stories.delete_one({
            "_id": ObjectId(story_id),
            "user_id": user_id
        })
        return result.deleted_count > 0
    except Exception as e:
        logging.error(f"删除故事时出错: {e}")
        return False

async def get_story_count_by_user(user_id: str) -> int:
    """获取用户的故事总数"""
    if not mongodb.connected or mongodb.db is None:
        return 0
    
    try:
        count = await mongodb.db.stories.count_documents({"user_id": user_id})
        return count
    except Exception as e:
        logging.error(f"获取故事数量时出错: {e}")
        return 0
