from typing import Generic, TypeVar, Optional, Any, Dict, List
from pydantic import BaseModel, Field

# Define a generic type variable for the data
T = TypeVar('T')

class StandardResponse(BaseModel, Generic[T]):
    """
    Standard response format for all API endpoints.
    
    Attributes:
        status: Status code, 0 for success, other values for specific errors
        data: The actual response data, can be any type
        message: Optional message, especially useful for error responses
    """
    status: int = Field(default=0, description="Status code, 0 for success, other values for specific errors")
    data: Optional[T] = Field(default=None, description="Response data")
    message: Optional[str] = Field(default=None, description="Response message, especially for errors")

    model_config = {
        "json_schema_extra": {
            "example": {
                "status": 0,
                "data": {},
                "message": "Operation successful"
            }
        }
    }

# Common response types
class MessageResponse(StandardResponse[Dict[str, Any]]):
    """Simple message response with no data"""
    data: Dict[str, Any] = {}
    message: str = "Operation successful"

# Error response codes
class ErrorCodes:
    """Error codes for the API"""
    GENERAL_ERROR = 1000
    AUTHENTICATION_ERROR = 1001
    AUTHORIZATION_ERROR = 1002
    VALIDATION_ERROR = 1003
    NOT_FOUND_ERROR = 1004
    CONFLICT_ERROR = 1005
    DATABASE_ERROR = 1006
