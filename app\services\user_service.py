from datetime import datetime, timezone
from typing import Optional
import logging
from bson import ObjectId
from ..db.mongodb import mongodb
from ..models.user import UserCreate, UserInDB, UserUpdate
from ..core.security import get_password_hash, verify_password

async def get_user_by_email(email: str) -> Optional[UserInDB]:
    """Get a user by email."""
    if not mongodb.connected or mongodb.db is None:
        return None

    user = await mongodb.db.users.find_one({"email": email})
    if user:
        return UserInDB(**user)
    return None

async def get_user_by_username(username: str) -> Optional[UserInDB]:
    """Get a user by username."""
    if not mongodb.connected or mongodb.db is None:
        return None

    user = await mongodb.db.users.find_one({"username": username})
    if user:
        return UserInDB(**user)
    return None

async def get_user_by_id(user_id: str) -> Optional[UserInDB]:
    """Get a user by ID."""
    if not mongodb.connected or mongodb.db is None:
        return None

    try:
        # 首先尝试使用字符串 ID 查询
        user = await mongodb.db.users.find_one({"_id": user_id})

        if user:
            return UserInDB(**user)

        # 如果字符串 ID 查询失败，尝试使用 ObjectId 查询
        if ObjectId.is_valid(user_id):
            obj_user = await mongodb.db.users.find_one({"_id": ObjectId(user_id)})
            if obj_user:
                return UserInDB(**obj_user)

        return None
    except Exception:
        return None

async def create_user(user_data: UserCreate) -> UserInDB:
    """Create a new user."""
    if not mongodb.connected or mongodb.db is None:
        raise ValueError("Database connection not available")

    # Check if user with the same email or username already exists
    existing_user = await get_user_by_email(user_data.email)
    if existing_user:
        raise ValueError("Email already registered")

    existing_user = await get_user_by_username(user_data.username)
    if existing_user:
        raise ValueError("Username already taken")

    # Create new user
    hashed_password = get_password_hash(user_data.password)
    current_time = datetime.now(timezone.utc)
    user_in_db = UserInDB(
        **user_data.model_dump(exclude={"password"}),
        hashed_password=hashed_password,
        created_at=current_time,
        updated_at=current_time
    )

    # Insert into database
    result = await mongodb.db.users.insert_one(user_in_db.model_dump(by_alias=True))

    # Update the ID field with the inserted ID
    user_in_db.id = result.inserted_id

    return user_in_db

async def authenticate_user(email: str, password: str) -> Optional[UserInDB]:
    """Authenticate a user."""
    if not mongodb.connected or mongodb.db is None:
        return None

    user = await get_user_by_email(email)
    if not user:
        return None
    if not verify_password(password, user.hashed_password):
        return None
    return user

async def update_user(user_id: str, user_data: UserUpdate) -> Optional[UserInDB]:
    """Update a user."""
    if not mongodb.connected or mongodb.db is None:
        return None

    user = await get_user_by_id(user_id)
    if not user:
        return None

    update_data = user_data.model_dump(exclude_unset=True)

    # If password is being updated, hash it
    if "password" in update_data:
        update_data["hashed_password"] = get_password_hash(update_data.pop("password"))

    update_data["updated_at"] = datetime.now(timezone.utc)

    # Update in database
    try:
        # 首先尝试使用字符串 ID 更新
        result = await mongodb.db.users.update_one(
            {"_id": user_id},
            {"$set": update_data}
        )

        # 如果没有更新任何文档，尝试使用 ObjectId
        if result.matched_count == 0 and ObjectId.is_valid(user_id):
            await mongodb.db.users.update_one(
                {"_id": ObjectId(user_id)},
                {"$set": update_data}
            )
    except Exception as e:
        logging.error(f"Error updating user: {e}")
        return None

    return await get_user_by_id(user_id)
