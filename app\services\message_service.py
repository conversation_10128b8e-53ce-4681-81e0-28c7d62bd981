from datetime import datetime, timezone
from typing import Optional, List
import logging
import random
from bson import ObjectId
from ..db.mongodb import mongodb
from ..models.message import MessageCreate, MessageInDB, MessageResponse

async def get_message_by_id(message_id: str, user_id: str = None) -> Optional[MessageInDB]:
    """根据ID获取消息，可选择验证用户权限"""
    logging.info(f"查询消息: message_id={message_id}, user_id={user_id}")
    
    if not mongodb.connected or mongodb.db is None:
        logging.error("数据库未连接")
        return None
    
    if not ObjectId.is_valid(message_id):
        logging.error(f"无效的ObjectId格式: {message_id}")
        return None
    
    # 构建查询条件 - 尝试两种ID格式
    queries_to_try = [
        {"_id": ObjectId(message_id)},  # ObjectId 格式
        {"_id": message_id}             # 字符串格式
    ]
    
    if user_id:
        queries_to_try = [
            {"_id": ObjectId(message_id), "user_id": user_id},
            {"_id": message_id, "user_id": user_id}
        ]
    
    message = None
    for query in queries_to_try:
        logging.info(f"尝试查询条件: {query}")
        message = await mongodb.db.messages.find_one(query)
        logging.info(f"查询结果: {message}")
        if message:
            break
    
    if message:
        return MessageInDB(**message)
    return None

async def get_messages_by_conversation(conversation_id: str, user_id: str, skip: int = 0, limit: int = 100) -> List[MessageInDB]:
    """获取会话的所有消息"""
    if not mongodb.connected or mongodb.db is None:
        return []
    
    messages = []
    cursor = mongodb.db.messages.find({
        "conversation_id": conversation_id,
        "user_id": user_id
    }).skip(skip).limit(limit).sort("sequence", 1)  # 按序号升序排列
    async for message in cursor:
        messages.append(MessageInDB(**message))
    return messages

async def get_next_sequence_number(conversation_id: str, user_id: str) -> int:
    """获取会话中下一个消息的序号"""
    if not mongodb.connected or mongodb.db is None:
        return 1
    
    try:
        # 查找该会话中最大的序号
        pipeline = [
            {"$match": {"conversation_id": conversation_id, "user_id": user_id}},
            {"$group": {"_id": None, "max_sequence": {"$max": "$sequence"}}}
        ]
        result = await mongodb.db.messages.aggregate(pipeline).to_list(1)
        
        if result and result[0]["max_sequence"] is not None:
            return result[0]["max_sequence"] + 1
        else:
            return 1
    except Exception as e:
        logging.error(f"获取下一个序号时出错: {e}")
        return 1

async def create_message(message_data: MessageCreate, conversation_id: str, user_id: str) -> MessageInDB:
    """创建新消息"""
    if not mongodb.connected or mongodb.db is None:
        raise ValueError("数据库连接不可用")
    
    # 验证角色
    valid_roles = ["user", "agent"]
    if message_data.role not in valid_roles:
        raise ValueError(f"无效的角色。允许的值: {', '.join(valid_roles)}")
    
    # 验证代理类型（如果是agent角色）
    if message_data.role == "agent" and message_data.agent_type:
        valid_agents = ["架构师", "记者", "执笔人"]
        if message_data.agent_type not in valid_agents:
            raise ValueError(f"无效的代理类型。允许的值: {', '.join(valid_agents)}")
    
    # 获取下一个序号
    sequence = await get_next_sequence_number(conversation_id, user_id)
    
    current_time = datetime.now(timezone.utc)
    message_in_db = MessageInDB(
        conversation_id=conversation_id,
        sequence=sequence,
        agent_type=message_data.agent_type,
        role=message_data.role,
        content=message_data.content,
        user_id=user_id,
        timestamp=current_time
    )
    
    # 插入数据库
    result = await mongodb.db.messages.insert_one(message_in_db.model_dump(by_alias=True))
    
    # 更新ID字段
    message_in_db.id = result.inserted_id
    
    return message_in_db

async def delete_message(message_id: str, user_id: str) -> bool:
    """删除消息"""
    if not mongodb.connected or mongodb.db is None:
        return False
    
    if not ObjectId.is_valid(message_id):
        return False
    
    try:
        # 先尝试 ObjectId 格式
        result = await mongodb.db.messages.delete_one({
            "_id": ObjectId(message_id),
            "user_id": user_id
        })
        
        # 如果没有删除任何文档，尝试字符串格式
        if result.deleted_count == 0:
            result = await mongodb.db.messages.delete_one({
                "_id": message_id,
                "user_id": user_id
            })
        
        return result.deleted_count > 0
    except Exception as e:
        logging.error(f"删除消息时出错: {e}")
        return False

def message_to_response(message: MessageInDB) -> MessageResponse:
    """将MessageInDB转换为MessageResponse"""
    return MessageResponse(
        id=str(message.id),
        conversation_id=message.conversation_id,
        sequence=message.sequence,
        agent_type=message.agent_type,
        role=message.role,
        content=message.content,
        user_id=message.user_id,
        timestamp=message.timestamp
    )

async def generate_ai_response(user_message: str, agent_type: str) -> str:
    """生成AI响应（模拟实现）"""
    # 这里是模拟的AI响应，实际项目中会调用真正的AI服务
    
    responses_by_agent = {
        "架构师": [
            f"作为架构师，我认为您的想法很有趣。让我们从故事的整体结构开始思考...",
            f"从架构的角度来看，这个故事需要一个清晰的三幕结构。让我为您分析一下...",
            f"我建议我们先确定故事的核心冲突和主题，然后再构建具体的情节线...",
            f"这个故事的设定很有潜力。让我们讨论一下世界观的构建..."
        ],
        "记者": [
            f"作为记者，我想深入了解更多细节。能告诉我更多关于角色背景的信息吗？",
            f"这个情节很吸引人！让我们挖掘一下背后的动机和原因...",
            f"我需要更多的事实和细节来丰富这个故事。您能描述一下具体的场景吗？",
            f"从新闻的角度来看，这个故事缺少一些关键信息。让我们一起补充..."
        ],
        "执笔人": [
            f"让我来帮您把这个想法转化为生动的文字。我建议这样开始...",
            f"这个场景很有画面感！让我用更具体的描述来展现它...",
            f"我觉得这里可以加入一些对话来增强戏剧性。比如...",
            f"从文学的角度，我们可以运用一些修辞手法来增强表现力..."
        ]
    }
    
    # 随机选择一个响应模板
    templates = responses_by_agent.get(agent_type, responses_by_agent["架构师"])
    response_template = random.choice(templates)
    
    # 添加一些针对用户消息的个性化内容
    if "故事" in user_message:
        response_template += "\n\n基于您提到的故事元素，我有以下建议..."
    elif "角色" in user_message:
        response_template += "\n\n关于角色塑造，我认为..."
    elif "情节" in user_message:
        response_template += "\n\n在情节发展方面..."
    
    return response_template

async def create_message_with_ai_response(message_data: MessageCreate, conversation_id: str, user_id: str, agent_type: str) -> tuple[MessageInDB, MessageInDB]:
    """创建用户消息并生成AI响应"""
    # 创建用户消息
    user_message = await create_message(message_data, conversation_id, user_id)
    
    # 生成AI响应
    ai_content = await generate_ai_response(message_data.content, agent_type)
    
    # 创建AI消息
    ai_message_data = MessageCreate(
        agent_type=agent_type,
        role="agent",
        content=ai_content
    )
    ai_message = await create_message(ai_message_data, conversation_id, user_id)
    
    return user_message, ai_message
