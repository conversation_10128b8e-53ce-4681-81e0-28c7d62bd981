FROM python:3.10-slim

WORKDIR /app

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    TZ=Asia/Shanghai

# 完全替换为阿里云镜像源并安装系统依赖
RUN echo "deb https://mirrors.aliyun.com/debian/ bullseye main contrib non-free" > /etc/apt/sources.list && \
    echo "deb https://mirrors.aliyun.com/debian/ bullseye-updates main contrib non-free" >> /etc/apt/sources.list && \
    echo "deb https://mirrors.aliyun.com/debian-security bullseye-security main contrib non-free" >> /etc/apt/sources.list && \
    # 设置apt-get选项以加快速度
    apt-get -o Acquire::http::Timeout=60 -o Acquire::retries=3 update && \
    apt-get -o Acquire::http::Timeout=60 -o Acquire::retries=3 install -y --no-install-recommends \
    build-essential \
    curl \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 设置时区（与系统依赖安装合并到一起可以减少层数，但这里保持分开以便于维护）
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 复制项目文件
COPY requirements.txt .

# 安装Python依赖
# 配置pip使用阿里云镜像源
RUN pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/ && \
    pip config set global.trusted-host mirrors.aliyun.com && \
    # 设置超时和重试次数
    pip install --no-cache-dir --timeout 60 --retries 3 -r requirements.txt && \
    pip install --no-cache-dir --timeout 60 --retries 3 email_validator

# 复制应用程序代码
COPY . .

# 创建非root用户并切换
RUN adduser --disabled-password --gecos "" appuser && \
    chown -R appuser:appuser /app
USER appuser

# 证书目录已存在于app/core/certs中

# 暴露端口
EXPOSE 8000

# 设置启动命令
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
