from fastapi import API<PERSON>outer, Depends, HTTPException, status
from fastapi.responses import JSONResponse

from ...models.user import User, UserUpdate
from ...services.user_service import update_user
from .auth import get_current_user
from ...core.response_utils import StandardJSONResponse, success_response

router = APIRouter()

@router.get("/me", response_model=None)
async def read_users_me(current_user: User = Depends(get_current_user)) -> JSONResponse:
    """
    Get current user.
    """
    # 排除 hashed_password 字段
    user_data = current_user.model_dump(by_alias=True)
    if "hashed_password" in user_data:
        del user_data["hashed_password"]

    return StandardJSONResponse(
        content=success_response(
            data=user_data,
            message="Current user retrieved successfully"
        )
    )

@router.put("/me", response_model=None)
async def update_user_me(
    user_data: UserUpdate,
    current_user: User = Depends(get_current_user)
) -> JSONResponse:
    """
    Update current user.
    """
    updated_user = await update_user(str(current_user.id), user_data)
    if not updated_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found",
        )

    # 排除 hashed_password 字段
    user_data = updated_user.model_dump(by_alias=True)
    if "hashed_password" in user_data:
        del user_data["hashed_password"]

    return StandardJSONResponse(
        content=success_response(
            data=user_data,
            message="User updated successfully"
        )
    )
