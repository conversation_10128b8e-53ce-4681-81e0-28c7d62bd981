from contextlib import asynccontextmanager
from fastapi import <PERSON><PERSON><PERSON>, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException

from .api.api import api_router
from .core.config import settings
from .core.response_utils import error_response, validation_error_response
from .db.mongodb import mongodb
from .models.response import Error<PERSON><PERSON>

@asynccontextmanager
async def lifespan(_: FastAPI):
    # Startup: Connect to the database
    await mongodb.connect_to_database()
    yield
    # Shutdown: Close the database connection
    await mongodb.close_database_connection()

# Import our custom response class
from .core.response_utils import StandardJ<PERSON>NResponse

# Create the FastAPI app with lifespan and custom default response class
app = FastAPI(
    title=settings.PROJECT_NAME,
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    debug=settings.DEBUG,
    lifespan=lifespan,
    default_response_class=StandardJSONResponse
)

# Set up CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add standardized response middleware
from .core.middleware import StandardResponseMiddleware
app.add_middleware(StandardResponseMiddleware)

# Exception handlers for standardized responses
@app.exception_handler(StarletteHTTPException)
async def http_exception_handler(_: Request, exc: StarletteHTTPException):
    """Handle HTTP exceptions with standardized response format."""
    status_code = exc.status_code
    error_code = ErrorCodes.GENERAL_ERROR

    # Map HTTP status codes to our error codes
    if status_code == status.HTTP_401_UNAUTHORIZED:
        error_code = ErrorCodes.AUTHENTICATION_ERROR
    elif status_code == status.HTTP_403_FORBIDDEN:
        error_code = ErrorCodes.AUTHORIZATION_ERROR
    elif status_code == status.HTTP_404_NOT_FOUND:
        error_code = ErrorCodes.NOT_FOUND_ERROR
    elif status_code == status.HTTP_409_CONFLICT:
        error_code = ErrorCodes.CONFLICT_ERROR

    return JSONResponse(
        status_code=status_code,
        content=error_response(
            error_code=error_code,
            message=exc.detail
        )
    )

@app.exception_handler(RequestValidationError)
async def validation_exception_handler(_: Request, exc: RequestValidationError):
    """Handle validation errors with standardized response format."""
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content=validation_error_response(
            message="Validation error",
            errors=exc.errors()
        )
    )

# Include API router
app.include_router(api_router, prefix=settings.API_V1_STR)

@app.get("/")
async def root():
    """Root endpoint with standardized response format."""
    return {
        "status": 0,
        "data": {"version": "1.0.0"},
        "message": "Welcome to the AI Writing Platform API"
    }
