from fastapi import APIRouter
from .endpoints import auth, users, stories, conversations, messages
from ..core.config import settings

api_router = APIRouter()

# Include the various endpoints
api_router.include_router(
    auth.router,
    prefix="/auth",
    tags=["Authentication"],
    responses={401: {"description": "Authentication failed"}}
)
api_router.include_router(
    users.router,
    prefix="/users",
    tags=["User Management"],
    responses={401: {"description": "Not authenticated"}}
)
api_router.include_router(
    stories.router,
    prefix="/stories",
    tags=["Story Management"],
    responses={401: {"description": "Not authenticated"}}
)
api_router.include_router(
    conversations.router,
    prefix="/conversations",
    tags=["Conversation Management"],
    responses={401: {"description": "Not authenticated"}}
)
api_router.include_router(
    messages.router,
    prefix="/conversations",  # 消息路由使用conversations前缀
    tags=["Message Management"],
    responses={401: {"description": "Not authenticated"}}
)
