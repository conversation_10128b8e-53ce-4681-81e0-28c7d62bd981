from fastapi import APIRouter
from .endpoints import auth, users, contents
from ..core.config import settings

api_router = APIRouter()

# Include the various endpoints
api_router.include_router(
    auth.router,
    prefix="/auth",
    tags=["Authentication"],
    responses={401: {"description": "Authentication failed"}}
)
api_router.include_router(
    users.router,
    prefix="/users",
    tags=["User Management"],
    responses={401: {"description": "Not authenticated"}}
)
api_router.include_router(
    contents.router,
    prefix="/contents",
    tags=["Content Management"],
    responses={401: {"description": "Not authenticated"}}
)
