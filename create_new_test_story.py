#!/usr/bin/env python3
"""
创建新的测试故事
"""
import asyncio
import sys
import os
from datetime import datetime, timezone

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.db.mongodb import mongodb
from bson import ObjectId

async def create_new_test_story():
    """创建新的测试故事"""
    print("=== 创建新的测试故事 ===")
    
    # 连接数据库
    await mongodb.connect_to_database()
    
    if not mongodb.connected:
        print("❌ 数据库连接失败")
        return
    
    print("✅ 数据库连接成功")
    
    try:
        # 查找第一个用户
        user = await mongodb.db.users.find_one({})
        if not user:
            print("❌ 没有找到用户，请先创建用户")
            return
        
        user_id = str(user['_id'])
        print(f"👤 找到用户: {user.get('username', 'N/A')} (ID: {user_id})")
        
        # 创建新的测试故事
        new_story_id = str(ObjectId())
        test_story = {
            "_id": new_story_id,  # 使用字符串格式的新ID
            "title": "新测试故事",
            "description": "这是一个用于测试删除功能的新故事",
            "status": "draft",
            "user_id": user_id,
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc)
        }
        
        # 插入测试故事
        result = await mongodb.db.stories.insert_one(test_story)
        print(f"✅ 新测试故事创建成功，ID: {new_story_id}")
        
        # 验证创建
        created_story = await mongodb.db.stories.find_one({"_id": new_story_id})
        if created_story:
            print(f"✅ 验证成功:")
            print(f"   ID: {created_story['_id']}")
            print(f"   标题: {created_story['title']}")
            print(f"   用户ID: {created_story['user_id']}")
            print(f"   状态: {created_story['status']}")
            print(f"\n🧪 您可以使用以下ID进行测试: {new_story_id}")
        else:
            print("❌ 验证失败，未找到创建的故事")
            
    except Exception as e:
        print(f"❌ 创建测试故事时出错: {e}")
    
    # 关闭数据库连接
    await mongodb.close_database_connection()

if __name__ == "__main__":
    asyncio.run(create_new_test_story())
