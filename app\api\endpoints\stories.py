from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from fastapi.responses import J<PERSON><PERSON>esponse

from ...models.story import StoryC<PERSON>, StoryUpdate, StoryResponse, StoryInDB
from ...models.user import User
from ...services.story_service import (
    create_story,
    get_story_by_id,
    get_stories_by_user,
    update_story,
    delete_story,
    get_story_count_by_user
)
from .auth import get_current_user
from ...core.response_utils import StandardJSONResponse, success_response, error_response
from ...models.response import ErrorCodes

router = APIRouter()

def story_to_response(story: StoryInDB) -> StoryResponse:
    """将Story模型转换为StoryResponse"""
    return StoryResponse(
        id=str(story.id),
        title=story.title,
        description=story.description,
        status=story.status,
        user_id=story.user_id,
        created_at=story.created_at,
        updated_at=story.updated_at
    )

@router.post("/", response_model=None)
async def create_new_story(
    story_data: Story<PERSON>reate,
    current_user: User = Depends(get_current_user)
) -> JSONResponse:
    """
    创建新故事
    """
    try:
        # 设置用户ID为当前用户
        story_data.user_id = str(current_user.id)
        
        # 验证状态值
        valid_statuses = ["draft", "active", "completed", "archived"]
        if story_data.status not in valid_statuses:
            return StandardJSONResponse(
                status_code=status.HTTP_400_BAD_REQUEST,
                content=error_response(
                    error_code=ErrorCodes.VALIDATION_ERROR,
                    message=f"无效的状态值。允许的值: {', '.join(valid_statuses)}"
                )
            )
        
        story = await create_story(story_data)
        story_response = story_to_response(story)
        
        return StandardJSONResponse(
            content=success_response(
                data=story_response.model_dump(),
                message="故事创建成功"
            )
        )
    except ValueError as e:
        return StandardJSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content=error_response(
                error_code=ErrorCodes.GENERAL_ERROR,
                message=str(e)
            )
        )
    except Exception as e:
        return StandardJSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=error_response(
                error_code=ErrorCodes.GENERAL_ERROR,
                message="创建故事时发生内部错误"
            )
        )

@router.get("/", response_model=None)
async def get_user_stories(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    current_user: User = Depends(get_current_user)
) -> JSONResponse:
    """
    获取用户故事列表
    """
    try:
        stories = await get_stories_by_user(str(current_user.id), skip, limit)
        total_count = await get_story_count_by_user(str(current_user.id))
        
        story_responses = [story_to_response(story) for story in stories]
        
        return StandardJSONResponse(
            content=success_response(
                data={
                    "stories": [story.model_dump() for story in story_responses],
                    "total": total_count,
                    "skip": skip,
                    "limit": limit
                },
                message="故事列表获取成功"
            )
        )
    except Exception as e:
        return StandardJSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=error_response(
                error_code=ErrorCodes.GENERAL_ERROR,
                message="获取故事列表时发生内部错误"
            )
        )

@router.get("/{story_id}", response_model=None)
async def get_story_detail(
    story_id: str,
    current_user: User = Depends(get_current_user)
) -> JSONResponse:
    """
    获取单个故事详情
    """
    try:
        # 先检查故事是否存在（不验证用户权限）
        story_exists = await get_story_by_id(story_id)
        if not story_exists:
            return StandardJSONResponse(
                status_code=status.HTTP_404_NOT_FOUND,
                content=error_response(
                    error_code=ErrorCodes.NOT_FOUND_ERROR,
                    message="故事不存在"
                )
            )

        # 再检查用户权限
        story = await get_story_by_id(story_id, str(current_user.id))
        if not story:
            return StandardJSONResponse(
                status_code=status.HTTP_403_FORBIDDEN,
                content=error_response(
                    error_code=ErrorCodes.AUTHORIZATION_ERROR,
                    message="您没有访问此故事的权限"
                )
            )
        
        story_response = story_to_response(story)
        
        return StandardJSONResponse(
            content=success_response(
                data=story_response.model_dump(),
                message="故事详情获取成功"
            )
        )
    except Exception as e:
        return StandardJSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=error_response(
                error_code=ErrorCodes.GENERAL_ERROR,
                message="获取故事详情时发生内部错误"
            )
        )

@router.patch("/{story_id}", response_model=None)
async def update_story_info(
    story_id: str,
    story_data: StoryUpdate,
    current_user: User = Depends(get_current_user)
) -> JSONResponse:
    """
    更新故事信息
    """
    try:
        # 验证状态值（如果提供了状态）
        if story_data.status is not None:
            valid_statuses = ["draft", "active", "completed", "archived"]
            if story_data.status not in valid_statuses:
                return StandardJSONResponse(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    content=error_response(
                        error_code=ErrorCodes.VALIDATION_ERROR,
                        message=f"无效的状态值。允许的值: {', '.join(valid_statuses)}"
                    )
                )

        # 先检查故事是否存在
        story_exists = await get_story_by_id(story_id)
        if not story_exists:
            return StandardJSONResponse(
                status_code=status.HTTP_404_NOT_FOUND,
                content=error_response(
                    error_code=ErrorCodes.NOT_FOUND_ERROR,
                    message="故事不存在"
                )
            )

        # 再尝试更新（会验证用户权限）
        updated_story = await update_story(story_id, story_data, str(current_user.id))
        if not updated_story:
            return StandardJSONResponse(
                status_code=status.HTTP_403_FORBIDDEN,
                content=error_response(
                    error_code=ErrorCodes.AUTHORIZATION_ERROR,
                    message="您没有修改此故事的权限"
                )
            )

        story_response = story_to_response(updated_story)

        return StandardJSONResponse(
            content=success_response(
                data=story_response.model_dump(),
                message="故事更新成功"
            )
        )
    except Exception as e:
        return StandardJSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=error_response(
                error_code=ErrorCodes.GENERAL_ERROR,
                message="更新故事时发生内部错误"
            )
        )

@router.delete("/{story_id}", response_model=None)
async def delete_story_by_id(
    story_id: str,
    current_user: User = Depends(get_current_user)
) -> JSONResponse:
    """
    删除故事
    """
    try:
        # 先检查故事是否存在
        story_exists = await get_story_by_id(story_id)
        if not story_exists:
            return StandardJSONResponse(
                status_code=status.HTTP_404_NOT_FOUND,
                content=error_response(
                    error_code=ErrorCodes.NOT_FOUND_ERROR,
                    message="故事不存在"
                )
            )

        # 再尝试删除（会验证用户权限）
        success = await delete_story(story_id, str(current_user.id))
        if not success:
            return StandardJSONResponse(
                status_code=status.HTTP_403_FORBIDDEN,
                content=error_response(
                    error_code=ErrorCodes.AUTHORIZATION_ERROR,
                    message="您没有删除此故事的权限"
                )
            )

        return StandardJSONResponse(
            status_code=status.HTTP_204_NO_CONTENT,
            content=None
        )
    except Exception as e:
        return StandardJSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=error_response(
                error_code=ErrorCodes.GENERAL_ERROR,
                message="删除故事时发生内部错误"
            )
        )
