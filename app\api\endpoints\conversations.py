from typing import List
from fastapi import APIRouter, Depends, Query
from fastapi.responses import JSONResponse

from ...models.conversation import ConversationCreate, ConversationUpdate, ConversationResponse, ConversationInDB
from ...models.user import User
from ...services.conversation_service import (
    create_conversation,
    get_conversation_by_id,
    get_conversations_by_user,
    update_conversation,
    delete_conversation,
    get_conversation_count_by_user
)
from .auth import get_current_user
from ...core.response_utils import StandardJSONResponse, success_response, error_response
from ...models.response import ErrorCodes

router = APIRouter()

def conversation_to_response(conversation: ConversationInDB) -> ConversationResponse:
    """将ConversationInDB转换为ConversationResponse"""
    return ConversationResponse(
        id=str(conversation.id),
        story_id=conversation.story_id,
        title=conversation.title,
        current_agent=conversation.current_agent,
        status=conversation.status,
        user_id=conversation.user_id,
        created_at=conversation.created_at,
        updated_at=conversation.updated_at
    )

@router.post("/", response_model=None)
async def create_new_conversation(
    conversation_data: ConversationCreate,
    current_user: User = Depends(get_current_user)
) -> JSONResponse:
    """
    创建新会话
    """
    try:
        conversation = await create_conversation(conversation_data, str(current_user.id))
        conversation_response = conversation_to_response(conversation)
        
        return StandardJSONResponse(
            content=success_response(
                data=conversation_response.model_dump(),
                message="会话创建成功"
            )
        )
    except ValueError as e:
        return StandardJSONResponse(
            status_code=400,
            content=error_response(
                error_code=ErrorCodes.VALIDATION_ERROR,
                message=str(e)
            )
        )
    except Exception as e:
        return StandardJSONResponse(
            status_code=500,
            content=error_response(
                error_code=ErrorCodes.GENERAL_ERROR,
                message="创建会话时发生内部错误"
            )
        )

@router.get("/", response_model=None)
async def get_user_conversations(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    current_user: User = Depends(get_current_user)
) -> JSONResponse:
    """
    获取用户会话列表
    """
    try:
        conversations = await get_conversations_by_user(str(current_user.id), skip, limit)
        total_count = await get_conversation_count_by_user(str(current_user.id))
        
        conversation_responses = [conversation_to_response(conv) for conv in conversations]
        
        return StandardJSONResponse(
            content=success_response(
                data={
                    "conversations": [conv.model_dump() for conv in conversation_responses],
                    "total": total_count,
                    "skip": skip,
                    "limit": limit
                },
                message="会话列表获取成功"
            )
        )
    except Exception as e:
        return StandardJSONResponse(
            status_code=500,
            content=error_response(
                error_code=ErrorCodes.GENERAL_ERROR,
                message="获取会话列表时发生内部错误"
            )
        )

@router.get("/{conversation_id}", response_model=None)
async def get_conversation_detail(
    conversation_id: str,
    current_user: User = Depends(get_current_user)
) -> JSONResponse:
    """
    获取单个会话详情
    """
    try:
        # 先检查会话是否存在（不验证用户权限）
        conversation_exists = await get_conversation_by_id(conversation_id)
        if not conversation_exists:
            return StandardJSONResponse(
                status_code=404,
                content=error_response(
                    error_code=ErrorCodes.NOT_FOUND_ERROR,
                    message="会话不存在"
                )
            )

        # 再检查用户权限
        conversation = await get_conversation_by_id(conversation_id, str(current_user.id))
        if not conversation:
            return StandardJSONResponse(
                status_code=403,
                content=error_response(
                    error_code=ErrorCodes.AUTHORIZATION_ERROR,
                    message="您没有访问此会话的权限"
                )
            )
        
        conversation_response = conversation_to_response(conversation)
        
        return StandardJSONResponse(
            content=success_response(
                data=conversation_response.model_dump(),
                message="会话详情获取成功"
            )
        )
    except Exception as e:
        return StandardJSONResponse(
            status_code=500,
            content=error_response(
                error_code=ErrorCodes.GENERAL_ERROR,
                message="获取会话详情时发生内部错误"
            )
        )

@router.patch("/{conversation_id}", response_model=None)
async def update_conversation_info(
    conversation_id: str,
    conversation_data: ConversationUpdate,
    current_user: User = Depends(get_current_user)
) -> JSONResponse:
    """
    更新会话状态
    """
    try:
        # 先检查会话是否存在
        conversation_exists = await get_conversation_by_id(conversation_id)
        if not conversation_exists:
            return StandardJSONResponse(
                status_code=404,
                content=error_response(
                    error_code=ErrorCodes.NOT_FOUND_ERROR,
                    message="会话不存在"
                )
            )

        # 再尝试更新（会验证用户权限）
        updated_conversation = await update_conversation(conversation_id, conversation_data, str(current_user.id))
        if not updated_conversation:
            return StandardJSONResponse(
                status_code=403,
                content=error_response(
                    error_code=ErrorCodes.AUTHORIZATION_ERROR,
                    message="您没有修改此会话的权限"
                )
            )

        conversation_response = conversation_to_response(updated_conversation)

        return StandardJSONResponse(
            content=success_response(
                data=conversation_response.model_dump(),
                message="会话更新成功"
            )
        )
    except ValueError as e:
        return StandardJSONResponse(
            status_code=400,
            content=error_response(
                error_code=ErrorCodes.VALIDATION_ERROR,
                message=str(e)
            )
        )
    except Exception as e:
        return StandardJSONResponse(
            status_code=500,
            content=error_response(
                error_code=ErrorCodes.GENERAL_ERROR,
                message="更新会话时发生内部错误"
            )
        )

@router.delete("/{conversation_id}", response_model=None)
async def delete_conversation_by_id(
    conversation_id: str,
    current_user: User = Depends(get_current_user)
) -> JSONResponse:
    """
    删除会话
    """
    try:
        # 先检查会话是否存在
        conversation_exists = await get_conversation_by_id(conversation_id)
        if not conversation_exists:
            return StandardJSONResponse(
                status_code=404,
                content=error_response(
                    error_code=ErrorCodes.NOT_FOUND_ERROR,
                    message="会话不存在"
                )
            )

        # 再尝试删除（会验证用户权限）
        success = await delete_conversation(conversation_id, str(current_user.id))
        if not success:
            return StandardJSONResponse(
                status_code=403,
                content=error_response(
                    error_code=ErrorCodes.AUTHORIZATION_ERROR,
                    message="您没有删除此会话的权限"
                )
            )

        return StandardJSONResponse(
            content=success_response(
                data={"deleted": True},
                message="会话删除成功"
            )
        )
    except Exception as e:
        return StandardJSONResponse(
            status_code=500,
            content=error_response(
                error_code=ErrorCodes.GENERAL_ERROR,
                message="删除会话时发生内部错误"
            )
        )
