FROM python:3.10-alpine

WORKDIR /app

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    TZ=Asia/Shanghai

# 使用阿里云镜像源
RUN if [ -f /etc/apk/repositories ]; then \
        sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories; \
    else \
        echo "https://mirrors.aliyun.com/alpine/v3.16/main" > /etc/apk/repositories && \
        echo "https://mirrors.aliyun.com/alpine/v3.16/community" >> /etc/apk/repositories; \
    fi

# 安装系统依赖
RUN apk update && apk add --no-cache \
    build-base \
    curl \
    tzdata \
    gcc \
    musl-dev \
    libffi-dev

# 设置时区
RUN cp /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 复制项目文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install -i https://mirrors.aliyun.com/pypi/simple/ --no-cache-dir -r requirements.txt && \
    pip install -i https://mirrors.aliyun.com/pypi/simple/ --no-cache-dir email_validator

# 复制应用程序代码
COPY . .

# 创建非root用户并切换
RUN adduser -D appuser && \
    chown -R appuser:appuser /app
USER appuser

# 证书目录已存在于app/core/certs中

# 暴露端口
EXPOSE 8000

# 设置启动命令
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
