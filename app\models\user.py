from datetime import datetime
from typing import Optional, Annotated
from pydantic import BaseModel, EmailStr, Field, BeforeValidator
from bson import ObjectId

# Helper function for ObjectId validation
def validate_object_id(v) -> str:
    if isinstance(v, ObjectId):
        return str(v)
    if isinstance(v, str):
        # 如果是有效的 ObjectId 格式，转换为字符串
        if ObjectId.is_valid(v):
            return str(v)
        # 否则直接返回字符串
        return v
    raise ValueError("Invalid ObjectId")

# Define a custom type for ObjectId
PyObjectId = Annotated[str, BeforeValidator(validate_object_id)]

class UserBase(BaseModel):
    email: EmailStr
    username: str
    full_name: Optional[str] = None
    disabled: bool = False

class UserCreate(UserBase):
    password: str

class UserInDB(UserBase):
    id: PyObjectId = Field(default_factory=lambda: str(ObjectId()), alias="_id")
    hashed_password: str
    created_at: datetime = Field(default_factory=lambda: datetime.now(datetime.timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(datetime.timezone.utc))

    model_config = {
        "populate_by_name": True,
        "json_schema_extra": {
            "example": {
                "email": "<EMAIL>",
                "username": "johndoe",
                "full_name": "John Doe",
                "disabled": False,
                "hashed_password": "hashedpassword",
                "created_at": "2023-01-01T00:00:00Z",
                "updated_at": "2023-01-01T00:00:00Z"
            }
        }
    }

class User(UserBase):
    id: PyObjectId = Field(alias="_id")
    created_at: datetime
    updated_at: datetime

    model_config = {
        "populate_by_name": True,
        "json_schema_extra": {
            "example": {
                "email": "<EMAIL>",
                "username": "johndoe",
                "full_name": "John Doe",
                "disabled": False,
                "created_at": "2023-01-01T00:00:00Z",
                "updated_at": "2023-01-01T00:00:00Z"
            }
        }
    }

class UserUpdate(BaseModel):
    email: Optional[EmailStr] = None
    username: Optional[str] = None
    full_name: Optional[str] = None
    password: Optional[str] = None
    disabled: Optional[bool] = None
