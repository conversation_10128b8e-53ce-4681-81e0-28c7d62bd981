from motor.motor_asyncio import AsyncIOMotorClient
from ..core.config import settings
import logging

class MongoDB:
    client: AsyncIOMotorClient = None
    db = None
    connected: bool = False

    async def connect_to_database(self):
        """Connect to MongoDB database."""
        try:
            # 显式设置 TLS 参数
            self.client = AsyncIOMotorClient(
                settings.MONGODB_URL,
                serverSelectionTimeoutMS=5000,
                tls=True,
                # tlsAllowInvalidCertificates=True,
                # tlsAllowInvalidHostnames=True,
                # 使用服务器证书作为客户端证书
                tlsCertificateKeyFile='app/core/certs/mongodb.pem',
                tlsCAFile='app/core/certs/mongodb.pem'
            )

            # Force a connection to verify that MongoDB is available
            await self.client.admin.command('ping')

            self.db = self.client[settings.MONGODB_DB]
            self.connected = True
            logging.info(f"Successfully connected to MongoDB at {settings.MONGODB_URL}")
        except Exception as e:
            self.connected = False
            self.db = None  # Ensure db is set to None on connection failure
            logging.error(f"MongoDB connection failed: {e}")
            # Continue without MongoDB - the app will still work for documentation

    async def close_database_connection(self):
        """Close database connection."""
        if self.client:
            self.client.close()
            self.connected = False
            logging.info("Closed connection with MongoDB")

# Create a singleton instance
mongodb = MongoDB()
