from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field
from bson import ObjectId
from .user import PyObjectId

class ContentBase(BaseModel):
    title: str
    content: str
    tags: Optional[List[str]] = []
    content_type: str  # e.g., "article", "story", "poem", etc.

class ContentCreate(ContentBase):
    user_id: str

class ContentInDB(ContentBase):
    id: PyObjectId = Field(default_factory=lambda: str(ObjectId()), alias="_id")
    user_id: str
    created_at: datetime = Field(default_factory=lambda: datetime.now(datetime.timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(datetime.timezone.utc))

    model_config = {
        "populate_by_name": True,
        "json_schema_extra": {
            "example": {
                "title": "Example Content",
                "content": "This is an example content.",
                "tags": ["example", "content"],
                "content_type": "article",
                "user_id": "user123",
                "created_at": "2023-01-01T00:00:00Z",
                "updated_at": "2023-01-01T00:00:00Z"
            }
        }
    }

class Content(ContentBase):
    id: PyObjectId = Field(alias="_id")
    user_id: str
    created_at: datetime
    updated_at: datetime

    model_config = {
        "populate_by_name": True,
        "json_schema_extra": {
            "example": {
                "title": "Example Content",
                "content": "This is an example content.",
                "tags": ["example", "content"],
                "content_type": "article",
                "user_id": "user123",
                "created_at": "2023-01-01T00:00:00Z",
                "updated_at": "2023-01-01T00:00:00Z"
            }
        }
    }

class ContentUpdate(BaseModel):
    title: Optional[str] = None
    content: Optional[str] = None
    tags: Optional[List[str]] = None
    content_type: Optional[str] = None
