from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field
from bson import ObjectId
from .user import PyObjectId

class StoryBase(BaseModel):
    title: str
    description: Optional[str] = None
    status: str = "draft"  # draft/active/completed/archived

class StoryCreate(StoryBase):
    user_id: Optional[str] = None  # 将在API层设置为当前用户ID

class StoryInDB(StoryBase):
    id: PyObjectId = Field(default_factory=lambda: str(ObjectId()), alias="_id")
    user_id: str
    created_at: datetime = Field(default_factory=lambda: datetime.now(datetime.timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(datetime.timezone.utc))

    model_config = {
        "populate_by_name": True,
        "json_schema_extra": {
            "example": {
                "title": "我的第一个故事",
                "description": "这是一个关于冒险的故事",
                "status": "draft",
                "user_id": "user123",
                "created_at": "2023-01-01T00:00:00Z",
                "updated_at": "2023-01-01T00:00:00Z"
            }
        }
    }

class Story(StoryBase):
    id: PyObjectId = Field(alias="_id")
    user_id: str
    created_at: datetime
    updated_at: datetime

    model_config = {
        "populate_by_name": True,
        "json_schema_extra": {
            "example": {
                "id": "507f1f77bcf86cd799439011",
                "title": "我的第一个故事",
                "description": "这是一个关于冒险的故事",
                "status": "draft",
                "user_id": "user123",
                "created_at": "2023-01-01T00:00:00Z",
                "updated_at": "2023-01-01T00:00:00Z"
            }
        }
    }

class StoryUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    status: Optional[str] = None

class StoryResponse(BaseModel):
    """用于API响应的故事模型"""
    id: str
    title: str
    description: Optional[str] = None
    status: str
    user_id: str
    created_at: datetime
    updated_at: datetime

    model_config = {
        "json_schema_extra": {
            "example": {
                "id": "507f1f77bcf86cd799439011",
                "title": "我的第一个故事",
                "description": "这是一个关于冒险的故事",
                "status": "draft",
                "user_id": "user123",
                "created_at": "2023-01-01T00:00:00Z",
                "updated_at": "2023-01-01T00:00:00Z"
            }
        }
    }
