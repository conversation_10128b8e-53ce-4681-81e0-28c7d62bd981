#!/usr/bin/env python3
"""
调试脚本：检查故事数据库状态
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.db.mongodb import mongodb
from bson import ObjectId

async def debug_stories():
    """调试故事数据库"""
    print("=== 故事数据库调试 ===")
    
    # 连接数据库
    await mongodb.connect_to_database()
    
    if not mongodb.connected:
        print("❌ 数据库连接失败")
        return
    
    print("✅ 数据库连接成功")
    
    # 检查stories集合
    try:
        # 获取所有故事
        stories_count = await mongodb.db.stories.count_documents({})
        print(f"📊 故事总数: {stories_count}")
        
        if stories_count > 0:
            print("\n📝 所有故事:")
            async for story in mongodb.db.stories.find({}):
                print(f"  - ID: {story['_id']}")
                print(f"    标题: {story.get('title', 'N/A')}")
                print(f"    用户ID: {story.get('user_id', 'N/A')}")
                print(f"    状态: {story.get('status', 'N/A')}")
                print()
        
        # 检查用户集合
        users_count = await mongodb.db.users.count_documents({})
        print(f"👥 用户总数: {users_count}")
        
        if users_count > 0:
            print("\n👤 所有用户:")
            async for user in mongodb.db.users.find({}):
                print(f"  - ID: {user['_id']}")
                print(f"    用户名: {user.get('username', 'N/A')}")
                print(f"    邮箱: {user.get('email', 'N/A')}")
                print()
                
    except Exception as e:
        print(f"❌ 查询数据时出错: {e}")
    
    # 关闭数据库连接
    await mongodb.close_database_connection()

if __name__ == "__main__":
    asyncio.run(debug_stories())
