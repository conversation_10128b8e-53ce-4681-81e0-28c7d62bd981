from datetime import datetime, timezone
from typing import Optional, List
import logging
from bson import ObjectId
from ..db.mongodb import mongodb
from ..models.conversation import ConversationCreate, ConversationInDB, ConversationUpdate

async def get_conversation_by_id(conversation_id: str, user_id: str = None) -> Optional[ConversationInDB]:
    """根据ID获取会话，可选择验证用户权限"""
    logging.info(f"查询会话: conversation_id={conversation_id}, user_id={user_id}")
    
    if not mongodb.connected or mongodb.db is None:
        logging.error("数据库未连接")
        return None
    
    if not ObjectId.is_valid(conversation_id):
        logging.error(f"无效的ObjectId格式: {conversation_id}")
        return None
    
    # 构建查询条件 - 尝试两种ID格式
    queries_to_try = [
        {"_id": ObjectId(conversation_id)},  # ObjectId 格式
        {"_id": conversation_id}             # 字符串格式
    ]
    
    if user_id:
        queries_to_try = [
            {"_id": ObjectId(conversation_id), "user_id": user_id},
            {"_id": conversation_id, "user_id": user_id}
        ]
    
    conversation = None
    for query in queries_to_try:
        logging.info(f"尝试查询条件: {query}")
        conversation = await mongodb.db.conversations.find_one(query)
        logging.info(f"查询结果: {conversation}")
        if conversation:
            break
    
    if conversation:
        return ConversationInDB(**conversation)
    return None

async def get_conversations_by_user(user_id: str, skip: int = 0, limit: int = 100) -> List[ConversationInDB]:
    """获取用户的所有会话"""
    if not mongodb.connected or mongodb.db is None:
        return []
    
    conversations = []
    cursor = mongodb.db.conversations.find({"user_id": user_id}).skip(skip).limit(limit).sort("updated_at", -1)
    async for conversation in cursor:
        conversations.append(ConversationInDB(**conversation))
    return conversations

async def get_conversations_by_story(story_id: str, user_id: str, skip: int = 0, limit: int = 100) -> List[ConversationInDB]:
    """获取特定故事的所有会话"""
    if not mongodb.connected or mongodb.db is None:
        return []
    
    conversations = []
    cursor = mongodb.db.conversations.find({
        "story_id": story_id, 
        "user_id": user_id
    }).skip(skip).limit(limit).sort("updated_at", -1)
    async for conversation in cursor:
        conversations.append(ConversationInDB(**conversation))
    return conversations

async def create_conversation(conversation_data: ConversationCreate, user_id: str) -> ConversationInDB:
    """创建新会话"""
    if not mongodb.connected or mongodb.db is None:
        raise ValueError("数据库连接不可用")
    
    # 验证代理类型
    valid_agents = ["架构师", "记者", "执笔人"]
    if conversation_data.current_agent not in valid_agents:
        raise ValueError(f"无效的代理类型。允许的值: {', '.join(valid_agents)}")
    
    # 验证状态
    valid_statuses = ["active", "paused", "completed"]
    if conversation_data.status not in valid_statuses:
        raise ValueError(f"无效的状态值。允许的值: {', '.join(valid_statuses)}")
    
    current_time = datetime.now(timezone.utc)
    conversation_in_db = ConversationInDB(
        **conversation_data.model_dump(),
        user_id=user_id,
        created_at=current_time,
        updated_at=current_time
    )
    
    # 插入数据库
    result = await mongodb.db.conversations.insert_one(conversation_in_db.model_dump(by_alias=True))
    
    # 更新ID字段
    conversation_in_db.id = result.inserted_id
    
    return conversation_in_db

async def update_conversation(conversation_id: str, conversation_data: ConversationUpdate, user_id: str) -> Optional[ConversationInDB]:
    """更新会话信息"""
    if not mongodb.connected or mongodb.db is None:
        return None
    
    # 首先验证会话存在且属于当前用户
    conversation = await get_conversation_by_id(conversation_id, user_id)
    if not conversation:
        return None
    
    # 准备更新数据
    update_data = conversation_data.model_dump(exclude_unset=True)
    if not update_data:  # 如果没有要更新的数据
        return conversation
    
    # 验证代理类型（如果提供）
    if "current_agent" in update_data:
        valid_agents = ["架构师", "记者", "执笔人"]
        if update_data["current_agent"] not in valid_agents:
            raise ValueError(f"无效的代理类型。允许的值: {', '.join(valid_agents)}")
    
    # 验证状态（如果提供）
    if "status" in update_data:
        valid_statuses = ["active", "paused", "completed"]
        if update_data["status"] not in valid_statuses:
            raise ValueError(f"无效的状态值。允许的值: {', '.join(valid_statuses)}")
    
    update_data["updated_at"] = datetime.now(timezone.utc)
    
    # 更新数据库 - 尝试两种ID格式
    try:
        # 先尝试 ObjectId 格式
        result = await mongodb.db.conversations.update_one(
            {"_id": ObjectId(conversation_id), "user_id": user_id},
            {"$set": update_data}
        )
        
        # 如果没有匹配到，尝试字符串格式
        if result.matched_count == 0:
            result = await mongodb.db.conversations.update_one(
                {"_id": conversation_id, "user_id": user_id},
                {"$set": update_data}
            )
        
        if result.matched_count == 0:
            return None
            
    except Exception as e:
        logging.error(f"更新会话时出错: {e}")
        return None
    
    return await get_conversation_by_id(conversation_id, user_id)

async def delete_conversation(conversation_id: str, user_id: str) -> bool:
    """删除会话"""
    if not mongodb.connected or mongodb.db is None:
        return False
    
    if not ObjectId.is_valid(conversation_id):
        return False
    
    try:
        # 先尝试 ObjectId 格式
        result = await mongodb.db.conversations.delete_one({
            "_id": ObjectId(conversation_id),
            "user_id": user_id
        })
        
        # 如果没有删除任何文档，尝试字符串格式
        if result.deleted_count == 0:
            result = await mongodb.db.conversations.delete_one({
                "_id": conversation_id,
                "user_id": user_id
            })
        
        return result.deleted_count > 0
    except Exception as e:
        logging.error(f"删除会话时出错: {e}")
        return False

async def get_conversation_count_by_user(user_id: str) -> int:
    """获取用户的会话总数"""
    if not mongodb.connected or mongodb.db is None:
        return 0

    try:
        count = await mongodb.db.conversations.count_documents({"user_id": user_id})
        return count
    except Exception as e:
        logging.error(f"获取会话数量时出错: {e}")
        return 0

async def get_conversation_count_by_story(story_id: str, user_id: str) -> int:
    """获取特定故事的会话总数"""
    if not mongodb.connected or mongodb.db is None:
        return 0

    try:
        count = await mongodb.db.conversations.count_documents({
            "story_id": story_id,
            "user_id": user_id
        })
        return count
    except Exception as e:
        logging.error(f"获取故事会话数量时出错: {e}")
        return 0
