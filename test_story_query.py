#!/usr/bin/env python3
"""
测试故事查询的脚本
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.db.mongodb import mongodb
from app.services.story_service import get_story_by_id
from bson import ObjectId

async def test_story_query():
    """测试故事查询"""
    print("=== 测试故事查询 ===")
    
    # 连接数据库
    await mongodb.connect_to_database()
    
    if not mongodb.connected:
        print("❌ 数据库连接失败")
        return
    
    print("✅ 数据库连接成功")
    
    story_id = "684444580a9b4b8c2cb268ec"
    user_id = "681b8bf501104210d1b66eb4"
    
    try:
        print(f"\n🔍 测试查询故事 ID: {story_id}")
        print(f"👤 用户 ID: {user_id}")
        
        # 测试1: 不验证用户权限的查询
        print("\n📝 测试1: 查询故事（不验证用户权限）")
        story_no_auth = await get_story_by_id(story_id)
        if story_no_auth:
            print("✅ 找到故事:")
            print(f"   标题: {story_no_auth.title}")
            print(f"   用户ID: {story_no_auth.user_id}")
            print(f"   状态: {story_no_auth.status}")
        else:
            print("❌ 未找到故事")
        
        # 测试2: 验证用户权限的查询
        print("\n📝 测试2: 查询故事（验证用户权限）")
        story_with_auth = await get_story_by_id(story_id, user_id)
        if story_with_auth:
            print("✅ 找到故事（用户有权限）:")
            print(f"   标题: {story_with_auth.title}")
            print(f"   用户ID: {story_with_auth.user_id}")
            print(f"   状态: {story_with_auth.status}")
        else:
            print("❌ 未找到故事或用户无权限")
        
        # 测试3: 直接数据库查询
        print("\n📝 测试3: 直接数据库查询")
        direct_query = await mongodb.db.stories.find_one({"_id": ObjectId(story_id)})
        if direct_query:
            print("✅ 直接查询找到故事:")
            print(f"   标题: {direct_query.get('title')}")
            print(f"   用户ID: {direct_query.get('user_id')}")
            print(f"   状态: {direct_query.get('status')}")
        else:
            print("❌ 直接查询未找到故事")
        
        # 测试4: 带用户权限的直接查询
        print("\n📝 测试4: 带用户权限的直接查询")
        auth_query = await mongodb.db.stories.find_one({
            "_id": ObjectId(story_id),
            "user_id": user_id
        })
        if auth_query:
            print("✅ 权限查询找到故事:")
            print(f"   标题: {auth_query.get('title')}")
            print(f"   用户ID: {auth_query.get('user_id')}")
            print(f"   状态: {auth_query.get('status')}")
        else:
            print("❌ 权限查询未找到故事")
            
    except Exception as e:
        print(f"❌ 测试时出错: {e}")
        import traceback
        traceback.print_exc()
    
    # 关闭数据库连接
    await mongodb.close_database_connection()

if __name__ == "__main__":
    asyncio.run(test_story_query())
