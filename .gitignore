# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 虚拟环境
venv/
ENV/
env/

# IDE相关
.idea/
.vscode/
*.swp
*.swo
.project
.pydevproject
.settings/

# 日志文件
*.log
logs/

# 本地配置文件
.env.local
.env.development.local
.env.test.local
.env.production.local

# 测试相关
.coverage
htmlcov/
.tox/
.nox/
.hypothesis/
.pytest_cache/
coverage.xml
*.cover
.hypothesis/

# 文档生成
docs/_build/
docs/api/

# 系统文件
.DS_Store
Thumbs.db
desktop.ini

# Docker相关
.docker/

# 临时文件
*.tmp
*.bak
*.swp
*~

# 数据库文件
*.sqlite3
*.db

# 其他
node_modules/
.sass-cache/
.ipynb_checkpoints/
