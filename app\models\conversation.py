from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field
from bson import ObjectId
from .user import PyObjectId

class ConversationBase(BaseModel):
    story_id: str
    title: str
    status: str = "active"  # active/paused/completed

class ConversationCreate(ConversationBase):
    # story_id, title, status 从请求体获取
    pass

class ConversationInDB(ConversationBase):
    id: PyObjectId = Field(default_factory=lambda: str(ObjectId()), alias="_id")
    user_id: str  # 会话所属用户
    created_at: datetime = Field(default_factory=lambda: datetime.now(datetime.timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(datetime.timezone.utc))

    model_config = {
        "populate_by_name": True,
        "json_schema_extra": {
            "example": {
                "story_id": "story123",
                "title": "与AI的对话",
                "status": "active",
                "user_id": "user123",
                "created_at": "2023-01-01T00:00:00Z",
                "updated_at": "2023-01-01T00:00:00Z"
            }
        }
    }

class Conversation(ConversationBase):
    id: PyObjectId = Field(alias="_id")
    user_id: str
    created_at: datetime
    updated_at: datetime

    model_config = {
        "populate_by_name": True,
        "json_schema_extra": {
            "example": {
                "id": "507f1f77bcf86cd799439011",
                "story_id": "story123",
                "title": "与AI的对话",
                "status": "active",
                "user_id": "user123",
                "created_at": "2023-01-01T00:00:00Z",
                "updated_at": "2023-01-01T00:00:00Z"
            }
        }
    }

class ConversationUpdate(BaseModel):
    title: Optional[str] = None
    status: Optional[str] = None

class ConversationResponse(BaseModel):
    """用于API响应的会话模型"""
    id: str
    story_id: str
    title: str
    status: str
    user_id: str
    created_at: datetime
    updated_at: datetime

    model_config = {
        "json_schema_extra": {
            "example": {
                "id": "507f1f77bcf86cd799439011",
                "story_id": "story123",
                "title": "与AI的对话",
                "status": "active",
                "user_id": "user123",
                "created_at": "2023-01-01T00:00:00Z",
                "updated_at": "2023-01-01T00:00:00Z"
            }
        }
    }
