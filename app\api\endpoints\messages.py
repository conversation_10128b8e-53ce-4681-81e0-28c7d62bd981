from typing import List
from fastapi import APIRouter, Depends, Query, Path
from fastapi.responses import JSONResponse

from ...models.message import MessageCreate, MessageResponse, MessageWithAIResponse, MessageInDB
from ...models.user import User
from ...services.message_service import (
    get_message_by_id,
    get_messages_by_conversation,
    get_message_count_by_conversation,
    create_message_with_ai_response,
    delete_message,
    message_to_response
)
from ...services.conversation_service import get_conversation_by_id
from .auth import get_current_user
from ...core.response_utils import StandardJSONResponse, success_response, error_response
from ...models.response import ErrorCodes

router = APIRouter()

@router.post("/{conversation_id}/messages", response_model=None)
async def send_message_and_get_ai_response(
    conversation_id: str = Path(..., description="会话ID"),
    message_data: MessageCreate = ...,
    current_user: User = Depends(get_current_user)
) -> JSONResponse:
    """
    发送消息并获取AI响应
    """
    try:
        # 验证会话是否存在且属于当前用户
        conversation = await get_conversation_by_id(conversation_id, str(current_user.id))
        if not conversation:
            return StandardJSONResponse(
                status_code=404,
                content=error_response(
                    error_code=ErrorCodes.NOT_FOUND_ERROR,
                    message="会话不存在或您没有访问权限"
                )
            )
        
        # 检查会话状态
        if conversation.status != "active":
            return StandardJSONResponse(
                status_code=400,
                content=error_response(
                    error_code=ErrorCodes.VALIDATION_ERROR,
                    message="只能在活跃状态的会话中发送消息"
                )
            )
        
        # 获取代理类型，如果用户没有指定则使用默认值
        agent_type = message_data.agent_type or "架构师"  # 默认使用架构师

        # 创建用户消息并获取AI响应
        user_message, ai_message = await create_message_with_ai_response(
            message_data,
            conversation_id,
            str(current_user.id),
            agent_type
        )
        
        # 转换为响应格式
        user_response = message_to_response(user_message)
        ai_response = message_to_response(ai_message)
        
        response_data = MessageWithAIResponse(
            user_message=user_response,
            ai_message=ai_response
        )
        
        return StandardJSONResponse(
            content=success_response(
                data=response_data.model_dump(),
                message="消息发送成功，AI已响应"
            )
        )
    except ValueError as e:
        return StandardJSONResponse(
            status_code=400,
            content=error_response(
                error_code=ErrorCodes.VALIDATION_ERROR,
                message=str(e)
            )
        )
    except Exception as e:
        return StandardJSONResponse(
            status_code=500,
            content=error_response(
                error_code=ErrorCodes.GENERAL_ERROR,
                message="发送消息时发生内部错误"
            )
        )

@router.get("/{conversation_id}/messages", response_model=None)
async def get_conversation_messages(
    conversation_id: str = Path(..., description="会话ID"),
    page: int = Query(1, ge=1, description="页码，从1开始"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    current_user: User = Depends(get_current_user)
) -> JSONResponse:
    """
    获取会话消息列表
    """
    try:
        # 验证会话是否存在且属于当前用户
        conversation = await get_conversation_by_id(conversation_id, str(current_user.id))
        if not conversation:
            return StandardJSONResponse(
                status_code=404,
                content=error_response(
                    error_code=ErrorCodes.NOT_FOUND_ERROR,
                    message="会话不存在或您没有访问权限"
                )
            )

        # 计算skip和limit
        skip = (page - 1) * page_size
        limit = page_size

        # 获取消息列表和总数
        messages = await get_messages_by_conversation(conversation_id, str(current_user.id), skip, limit)
        total_count = await get_message_count_by_conversation(conversation_id, str(current_user.id))

        # 计算总页数
        total_pages = (total_count + page_size - 1) // page_size

        message_responses = [message_to_response(msg) for msg in messages]

        return StandardJSONResponse(
            content=success_response(
                data={
                    "messages": [msg.model_dump() for msg in message_responses],
                    "pagination": {
                        "page": page,
                        "page_size": page_size,
                        "total": total_count,
                        "total_pages": total_pages
                    },
                    "conversation_id": conversation_id
                },
                message="消息列表获取成功"
            )
        )
    except Exception as e:
        return StandardJSONResponse(
            status_code=500,
            content=error_response(
                error_code=ErrorCodes.GENERAL_ERROR,
                message="获取消息列表时发生内部错误"
            )
        )

@router.get("/messages/{message_id}", response_model=None)
async def get_message_detail(
    message_id: str,
    current_user: User = Depends(get_current_user)
) -> JSONResponse:
    """
    获取单条消息详情
    """
    try:
        # 先检查消息是否存在（不验证用户权限）
        message_exists = await get_message_by_id(message_id)
        if not message_exists:
            return StandardJSONResponse(
                status_code=404,
                content=error_response(
                    error_code=ErrorCodes.NOT_FOUND_ERROR,
                    message="消息不存在"
                )
            )

        # 再检查用户权限
        message = await get_message_by_id(message_id, str(current_user.id))
        if not message:
            return StandardJSONResponse(
                status_code=403,
                content=error_response(
                    error_code=ErrorCodes.AUTHORIZATION_ERROR,
                    message="您没有访问此消息的权限"
                )
            )
        
        message_response = message_to_response(message)
        
        return StandardJSONResponse(
            content=success_response(
                data=message_response.model_dump(),
                message="消息详情获取成功"
            )
        )
    except Exception as e:
        return StandardJSONResponse(
            status_code=500,
            content=error_response(
                error_code=ErrorCodes.GENERAL_ERROR,
                message="获取消息详情时发生内部错误"
            )
        )

@router.delete("/messages/{message_id}", response_model=None)
async def delete_message_by_id(
    message_id: str,
    current_user: User = Depends(get_current_user)
) -> JSONResponse:
    """
    删除消息
    """
    try:
        # 先检查消息是否存在
        message_exists = await get_message_by_id(message_id)
        if not message_exists:
            return StandardJSONResponse(
                status_code=404,
                content=error_response(
                    error_code=ErrorCodes.NOT_FOUND_ERROR,
                    message="消息不存在"
                )
            )

        # 再尝试删除（会验证用户权限）
        success = await delete_message(message_id, str(current_user.id))
        if not success:
            return StandardJSONResponse(
                status_code=403,
                content=error_response(
                    error_code=ErrorCodes.AUTHORIZATION_ERROR,
                    message="您没有删除此消息的权限"
                )
            )

        return StandardJSONResponse(
            content=success_response(
                data={"deleted": True},
                message="消息删除成功"
            )
        )
    except Exception as e:
        return StandardJSONResponse(
            status_code=500,
            content=error_response(
                error_code=ErrorCodes.GENERAL_ERROR,
                message="删除消息时发生内部错误"
            )
        )
