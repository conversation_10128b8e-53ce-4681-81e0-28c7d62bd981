# Git
.git
.gitignore

# Docker
Dockerfile
docker-compose.yml
.dockerignore

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
ENV/

# IDE
.idea/
.vscode/
*.swp
*.swo

# Testing
.coverage
htmlcov/
.pytest_cache/
.tox/

# Logs
logs/
*.log

# Local environment
.env.local
.env.development.local
.env.test.local
.env.production.local
