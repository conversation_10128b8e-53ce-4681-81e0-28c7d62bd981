from datetime import <PERSON><PERSON><PERSON>
from typing import Any
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm, OAuth2PasswordBearer
from jose import JWTError, jwt
from pydantic import BaseModel

from ...core.config import settings
from ...core.security import create_access_token
from ...models.user import User, UserCreate
from ...services.user_service import authenticate_user, create_user, get_user_by_id

router = APIRouter()

# OAuth2 scheme for token authentication - use swagger_token for Swagger UI
oauth2_scheme = OAuth2PasswordBearer(tokenUrl=f"{settings.API_V1_STR}/auth/swagger_token")

class Token(BaseModel):
    access_token: str
    token_type: str
    user: dict = None  # 添加用户数据字段

class TokenPayload(BaseModel):
    sub: str

class LoginRequest(BaseModel):
    """用于 JSON 格式登录请求的模型"""
    username: str  # 实际上是邮箱
    password: str

@router.post(
    "/login",
    response_model=Token,
    summary="User Login",
    description="Authenticate user and return an access token",
    responses={
        200: {"description": "Successful login"},
        401: {"description": "Incorrect email or password"}
    }
)
async def login_for_access_token(login_data: LoginRequest) -> Any:
    """
    User login endpoint. Returns an access token for authenticated requests.

    - **username**: Email address of the user (required)
    - **password**: User password (required)

    Returns an access token that should be included in the Authorization header
    of subsequent requests as a Bearer token.
    """
    user = await authenticate_user(login_data.username, login_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": str(user.id)}, expires_delta=access_token_expires
    )

    # 获取用户数据（排除 hashed_password 字段）
    user_data = user.model_dump(by_alias=True)
    if "hashed_password" in user_data:
        del user_data["hashed_password"]

    # Return token and user data in the standardized format
    token_data = {
        "access_token": access_token,
        "token_type": "bearer",
        "user": user_data
    }
    return token_data

@router.post(
    "/swagger_token",
    response_model=Token,
    summary="Token for Swagger UI",
    description="Authenticate user and return an access token in the original format for Swagger UI",
    include_in_schema=True  # Make it visible in Swagger UI
)
async def swagger_token(form_data: OAuth2PasswordRequestForm = Depends()) -> Any:
    """
    Token endpoint specifically for Swagger UI authentication.
    Returns the token in the original format that Swagger UI expects.

    - **username**: Email address of the user (required)
    - **password**: User password (required)

    Returns an access token that should be included in the Authorization header
    of subsequent requests as a Bearer token.
    """
    user = await authenticate_user(form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": str(user.id)}, expires_delta=access_token_expires
    )

    # 获取用户数据（排除 hashed_password 字段）
    user_data = user.model_dump(by_alias=True)
    if "hashed_password" in user_data:
        del user_data["hashed_password"]

    # Return token in the original format (not wrapped in standardized format)
    # 使用 jsonable_encoder 处理 datetime 对象
    from fastapi.encoders import jsonable_encoder
    from fastapi.responses import JSONResponse

    response_content = {
        "access_token": access_token,
        "token_type": "bearer",
        "user": user_data
    }

    # 使用 jsonable_encoder 处理 datetime 对象
    json_compatible_content = jsonable_encoder(response_content)

    return JSONResponse(content=json_compatible_content)

@router.post(
    "/register",
    response_model=User,
    summary="Register New User",
    description="Create a new user account",
    responses={
        200: {"description": "User successfully registered"},
        400: {"description": "Email already registered or username already taken"}
    }
)
async def register_user(user_data: UserCreate) -> Any:
    """
    Register a new user account.

    - **email**: Valid email address (required)
    - **username**: Unique username (required)
    - **password**: User password (required)
    - **full_name**: User's full name (optional)

    Returns the created user object (without the password).
    """
    try:
        user = await create_user(user_data)
        return user
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )

async def get_current_user(token: str = Depends(oauth2_scheme)) -> User:
    """
    Get the current user from the token.
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )
        user_id: str = payload.get("sub")
        if user_id is None:
            raise credentials_exception
        token_data = TokenPayload(sub=user_id)
    except JWTError:
        raise credentials_exception

    user = await get_user_by_id(token_data.sub)
    if user is None:
        raise credentials_exception

    if user.disabled:
        raise HTTPException(status_code=400, detail="Inactive user")

    return user
