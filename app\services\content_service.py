from datetime import datetime
from typing import Optional, List
from bson import ObjectId
from ..db.mongodb import mongodb
from ..models.content import ContentCreate, ContentInDB, Content, ContentUpdate

async def get_content_by_id(content_id: str) -> Optional[ContentInDB]:
    """Get content by ID."""
    if not ObjectId.is_valid(content_id):
        return None
    
    content = await mongodb.db.contents.find_one({"_id": ObjectId(content_id)})
    if content:
        return ContentInDB(**content)
    return None

async def get_contents_by_user(user_id: str, skip: int = 0, limit: int = 100) -> List[ContentInDB]:
    """Get all contents for a user."""
    contents = []
    cursor = mongodb.db.contents.find({"user_id": user_id}).skip(skip).limit(limit)
    async for content in cursor:
        contents.append(ContentInDB(**content))
    return contents

async def create_content(content_data: ContentCreate) -> ContentInDB:
    """Create new content."""
    content_in_db = ContentInDB(
        **content_data.model_dump(),
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow()
    )
    
    # Insert into database
    result = await mongodb.db.contents.insert_one(content_in_db.model_dump(by_alias=True))
    
    # Update the ID field with the inserted ID
    content_in_db.id = result.inserted_id
    
    return content_in_db

async def update_content(content_id: str, content_data: ContentUpdate) -> Optional[ContentInDB]:
    """Update content."""
    content = await get_content_by_id(content_id)
    if not content:
        return None
    
    update_data = content_data.model_dump(exclude_unset=True)
    update_data["updated_at"] = datetime.utcnow()
    
    # Update in database
    await mongodb.db.contents.update_one(
        {"_id": ObjectId(content_id)},
        {"$set": update_data}
    )
    
    return await get_content_by_id(content_id)

async def delete_content(content_id: str) -> bool:
    """Delete content."""
    if not ObjectId.is_valid(content_id):
        return False
    
    result = await mongodb.db.contents.delete_one({"_id": ObjectId(content_id)})
    return result.deleted_count > 0
